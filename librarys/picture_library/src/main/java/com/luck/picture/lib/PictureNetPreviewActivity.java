package com.luck.picture.lib;

import android.graphics.Picture;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.FragmentActivity;
import androidx.viewpager.widget.ViewPager;

import com.luck.picture.lib.adapter.SimpleFragmentAdapter;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.widget.PreviewViewPager;

import java.util.ArrayList;
import java.util.List;

/**
 * 预览网络图片
 */
public class PictureNetPreviewActivity extends FragmentActivity {

    //url
    public static final String PICTURE_NET_PREVIEW_PATH_LIST = "urlList";
    //位置
    public static final String PICTURE_NET_PREVIEW_PATH_POSITION = "position";

    public PreviewViewPager previewPager;
    public ImageView ivPictureBack;
    public TextView tvPictureTitle;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.picture_net_preview);
        List<String> urlList = getIntent().getStringArrayListExtra(PICTURE_NET_PREVIEW_PATH_LIST);
        int position = getIntent().getIntExtra(PICTURE_NET_PREVIEW_PATH_POSITION, 0);
        previewPager = findViewById(R.id.PreviewPager);
        ivPictureBack = findViewById(R.id.ivPictureBack);
        tvPictureTitle = findViewById(R.id.tvPictureTitle);
        findViewById(R.id.ivPictureBack).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        if (urlList == null || urlList.isEmpty()) return;
        tvPictureTitle.setText(position + 1 + "/" + urlList.size());
        previewPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener(){
            @Override
            public void onPageSelected(int i) {
                tvPictureTitle.setText(i + 1 + "/" + urlList.size());
            }
        });
        List<LocalMedia> localMediaList = new ArrayList<>();
        for (String s : urlList) {
            localMediaList.add(new LocalMedia(s, 0, PictureConfig.TYPE_IMAGE, PictureConfig.IMAGE));
        }
        previewPager.setAdapter(new SimpleFragmentAdapter(localMediaList, this, null));
        previewPager.setCurrentItem(position);
    }
}