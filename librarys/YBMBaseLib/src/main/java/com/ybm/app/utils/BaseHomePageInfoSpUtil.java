package com.ybm.app.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.ybm.app.common.BaseYBMApp;

public class BaseHomePageInfoSpUtil {
    private static final String SP_NAME = "home_page_info";
    private static  SharedPreferences sp =null;

    public static void writeBoolean( String key, boolean value) {
        getEdit().putBoolean(key, value).apply();
    }
    public static boolean readBoolean(String key, boolean defValue) {
        return getSp().getBoolean(key, defValue);
    }

    public static void writeString(String key, String value) {
        getEdit().putString(key, value).apply();
    }

    public static boolean commit(){
        return getEdit().commit();
    }

    public static void remove(String key){
        getEdit().remove(key).apply();
    }

    public static String readString( String key, String defValue) {
        return getSp().getString(key, defValue);
    }

    public static void writeInt(String key, int value) {
        getEdit().putInt(key, value).apply();
    }

    public static int readInt(String key, int defValue) {
        return getSp().getInt(key, defValue);
    }

    private static SharedPreferences.Editor getEdit(){
       return getSp().edit();
    }
    private static SharedPreferences getSp() {
        if(sp == null){
          sp = getContext().getSharedPreferences(SP_NAME,
                Context.MODE_PRIVATE);
        }
        return sp;
    }

    private static  Context getContext(){
        return BaseYBMApp.getAppContext();
    }
}
