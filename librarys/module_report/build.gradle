plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace 'com.ydmmarket.report'
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
}

dependencies {
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    //极光埋点 含全埋点等多个模块
    implementation deps.jgAnalysysVersion
    implementation project(path: ':YBMBaseLib')
    implementation project(path: ':common')
}