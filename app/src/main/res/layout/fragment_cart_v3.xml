<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF2F3F4">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartrefresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_aptitude_tip">

        <com.ybmmarket20.view.HomeSteadyHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_shop_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_10" />

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--  头部吸顶  -->
    <FrameLayout
        android:id="@+id/header_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:background="#FFF2F3F4"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_aptitude_tip"
        tools:visibility="visible" />
    <!--   顶部资质提示 -->
    <include
        android:id="@+id/layout_aptitude_tip"
        layout="@layout/layout_aptitude_overdue_tip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_cart_title"
        tools:visibility="visible" />

    <View
        android:id="@+id/status_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />
    <!--  头部bar  -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_cart_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_44"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/status_bar">


        <TextView
            android:id="@+id/tv_title_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_15"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_17"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:drawableLeft="@drawable/ic_back"
            tools:text="购物车" />

        <TextView
            android:id="@+id/tv_coupon_cross_shop"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginRight="@dimen/dimen_dp_15"
            android:drawableLeft="@drawable/icon_cart_coupon_all"
            android:drawablePadding="6dp"
            android:gravity="center_vertical"
            android:text="平台券"
            android:textColor="#F62526"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_devider_between_coupon_edit"
            android:layout_width="1dp"
            android:layout_height="15dp"
            android:layout_marginLeft="@dimen/dimen_dp_4"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:background="@drawable/divider_shop_desc"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_coupon_cross_shop"
            app:layout_constraintTop_toTopOf="parent" />


        <CheckBox
            android:id="@+id/tv_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:padding="@dimen/dimen_dp_10"
            android:text="编辑"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_devider_between_coupon_edit"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 底部跨点券领取提示 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_cart_bottom_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#FFF7E9"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/ll_freight_over_weight_tips"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_cart_bottom_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dimen_dp_5"
            android:ellipsize="end"
            android:maxLines="2"
            android:paddingLeft="@dimen/dimen_dp_10"
            android:paddingTop="@dimen/dimen_dp_8"
            android:paddingRight="@dimen/dimen_dp_10"
            android:textColor="@color/text_color_333333"
            android:paddingBottom="@dimen/dimen_dp_8"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_bottom_tips_action"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="跨店优惠券：已减30元，再买1230元，用券最高可再减120元, 已减30元，再买1230元，用券最高可再减120元,已减30元，再买1230元，用券最高可再减120元,已减30元，再买1230元，用券最高可再减120元" />


        <TextView
            android:id="@+id/tv_bottom_tips_action"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:drawableRight="@drawable/ic_arrow"
            android:drawablePadding="@dimen/dimen_dp_5"
            android:gravity="center_vertical"
            android:text="去凑单"
            android:textColor="@color/color_ff3024"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_freight_over_weight_tips"
        android:layout_width="0dp"
        android:layout_height="38dp"
        android:background="@color/color_fff7ef"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/cl_bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_over_weight_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_weight="1"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:text="@string/str_freight_over_weight_cart_bottom_tip"
            android:textColor="@color/colors_99664D"
            android:textSize="11dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtv_over_weight_look"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingTop="3dp"
            android:paddingEnd="8dp"
            android:paddingBottom="3dp"
            android:text="@string/str_freight_over_weight_cart_bottom_look"
            android:textColor="@color/colors_99664D"
            android:textSize="11dp"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/colors_99664D"
            app:rv_strokeWidth="1dp" />

    </LinearLayout>

    <!--优惠明细上拉弹框-->
    <include
        android:id="@+id/fl_cart_discount"
        layout="@layout/show_dicount_cart_pop"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/cl_bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1"
        tools:visibility="gone" />

    <!--  结算bar  -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_48"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:divider="@drawable/divider_line_base_1px"
        android:elevation="3dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible">


        <CheckBox
            android:id="@+id/shop_check"
            style="@style/CustomCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/cart_tv_edit"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dimen_dp_5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="全选"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13"
            android:visibility="visible"
            app:layout_constraintLeft_toRightOf="@id/shop_check"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_cart_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dimen_dp_6"
            android:layout_marginRight="@dimen/dimen_dp_5"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            android:textStyle="bold"
            app:layout_constraintRight_toLeftOf="@id/btn_commit"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="总计: ¥137.50"
            tools:visibility="gone" />

        <LinearLayout
            android:id="@+id/ll_cart_total_loading"
            android:layout_width="@dimen/dimen_dp_100"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_6"
            android:layout_marginRight="@dimen/dimen_dp_5"
            android:visibility="gone"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintRight_toLeftOf="@id/btn_commit"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_292933"
                android:textSize="@dimen/dimen_dp_15"
                android:textStyle="bold"
                android:text="总计：" />

            <TextView
                android:id="@+id/tv_cart_total_loading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_9494A6"
                android:textSize="@dimen/dimen_dp_12"
                android:text="计算中..." />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_cart_promotion_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dimen_dp_3"
            android:layout_marginBottom="@dimen/dimen_dp_5"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/dimen_dp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_cart_discount_detail"
            tools:text="促销减: ¥120.30 用券减: ¥120.30" />

        <TextView
            android:id="@+id/tv_cart_discount_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dimen_dp_5"
            android:layout_marginBottom="@dimen/dimen_dp_4"
            android:background="@drawable/bg_cart_discount"
            android:drawableRight="@drawable/icon_arrow_up"
            android:drawablePadding="2dp"
            android:paddingLeft="@dimen/dimen_dp_5"
            android:paddingTop="1dp"
            android:paddingRight="@dimen/dimen_dp_4"
            android:paddingBottom="@dimen/dimen_dp_1"
            android:text="明细"
            android:textColor="@color/color_292933"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/btn_commit"
            tools:visibility="visible" />


        <Button
            android:id="@+id/btn_commit"
            android:layout_width="91dp"
            android:layout_height="0dp"
            android:background="@color/base_colors"
            android:textColor="@color/white"
            android:textSize="17sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="去结算" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_cart_loading"
        android:layout_width="@dimen/dimen_dp_40"
        android:layout_height="@dimen/dimen_dp_40"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>