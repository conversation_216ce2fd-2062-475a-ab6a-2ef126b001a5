<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white" >

    <TextView
        android:id="@+id/tv_refund_reason_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_42"
        android:textSize="@dimen/dimen_dp_16"
        android:textColor="@color/color_292933"
        android:gravity="center"
        android:text="申请原因"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_refund_reason_close"
        android:layout_width="@dimen/dimen_dp_32"
        android:layout_height="@dimen/dimen_dp_32"
        app:layout_constraintTop_toTopOf="@+id/tv_refund_reason_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_refund_reason_title"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/dimen_dp_8"
        android:src="@drawable/icon_refund_reason"
        android:padding="@dimen/dimen_dp_8" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_refund_reason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_3"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        tools:itemCount="6"
        tools:listitem="@layout/item_refund_reason_parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_refund_reason_title" />
    
    <EditText
        android:id="@+id/et_refund_reason_desc"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_90"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        app:layout_constraintTop_toBottomOf="@+id/rv_refund_reason"
        android:gravity="start"
        android:text=""
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_292933"
        android:hint="请描述具体原因（必填）："
        android:background="@drawable/icon_refund_reason_desc"
        android:visibility="gone"
        android:inputType="text"
        tools:ignore="Autofill"
        android:paddingTop="@dimen/dimen_dp_18" />
    
    <TextView
        android:id="@+id/tv_refund_reason_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:background="@drawable/shape_refund_reason_btn"
        app:layout_constraintTop_toBottomOf="@+id/rv_refund_reason"
        android:text="确认"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_16"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_96"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>