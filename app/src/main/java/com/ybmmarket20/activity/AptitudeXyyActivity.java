package com.ybmmarket20.activity;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeXyyBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.AptitudePdfUrlBean;
import com.ybmmarket20.common.AppUpdateManagerV2;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.ShowBottomCommonDialog;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

import static com.ybmmarket20.activity.AptitudeXyyEmailActivity.FLAG_ALL;
import static com.ybmmarket20.activity.AptitudeXyyEmailActivity.FLAG_ONE;
import static com.ybmmarket20.activity.AptitudeXyyEmailActivity.IDS_ALL;

/**
 * 小药药资质
 */
@Router("aptitudexyy")
public class AptitudeXyyActivity extends BaseActivity implements BaseQuickAdapter.OnItemClickListener {


    @Bind(R.id.rv_xyy_aptitude)
    CommonRecyclerView recyclerView;
    @Bind(R.id.tv_download_to_email)
    TextView tvDownToEmail;

    private YBMBaseAdapter adapter;
    private List<AptitudeXyyBean.RowsBean> list = new ArrayList<>();
    private String mTitle;

    protected int getContentViewId() {
        return R.layout.activity_aptitude_xyy;
    }

    @Override
    protected void initData() {
        setTitle("小药药资质");
        initRecyclerView();
    }

    private void initRecyclerView() {
        adapter = new YBMBaseAdapter<AptitudeXyyBean.RowsBean>(R.layout.item_aptitude_xyy, list) {
            @Override
            protected void bindItemView(YBMBaseHolder ybmBaseHolder, AptitudeXyyBean.RowsBean bean) {
                ybmBaseHolder.setText(R.id.tv_content, ybmBaseHolder.getAdapterPosition() + 1 + "、" + bean.getName());

                ybmBaseHolder.getView(R.id.iv_more).setOnClickListener(v -> {//查看更多
                    ShowBottomCommonDialog dialog = new ShowBottomCommonDialog(getMySelf());
                    dialog.show();
                    dialog.setOnItemChooseListener(new ShowBottomCommonDialog.OnItemChooseListener() {
                        @Override
                        public void OnTopClick() {//发送到邮箱
                            dialog.dismiss();
                            RoutersUtils.open("ybmpage://aptitudexyyemail?flag=" + FLAG_ONE + "&contractId=" + bean.getId());
                        }

                        @Override
                        public void OnBottomClick() {//分享至微信
                            dialog.dismiss();
                            getAptitudeXyyPdfUrl(bean.getId());
                            mTitle = bean.getName();
                        }

                        @Override
                        public void OnCancelClick() {
                            dialog.dismiss();
                        }
                    });
                });

            }

        };
        View emptyView = getLayoutInflater().inflate(R.layout.layout_empty_view, null);
        emptyView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        adapter.setEmptyView(emptyView);
        //recyclerView.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无数据");
        adapter.setOnItemClickListener(this);
        recyclerView.setListener(new CommonRecyclerView.Listener() {

            @Override
            public void onRefresh() {
                getAptitudeXyyList();
            }

            @Override
            public void onLoadMore() {
            }
        });
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setAdapter(adapter);
        recyclerView.setLayoutManager(new WrapLinearLayoutManager(this));

    }

    private void getAptitudeXyyList() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.GET_APTITUDE_XYY_LIST, params, new BaseResponse<AptitudeXyyBean>() {

            @Override
            public void onFailure(NetError error) {
                recyclerView.setRefreshing(false);
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeXyyBean> baseBean, AptitudeXyyBean data) {
                dismissProgress();
                recyclerView.setRefreshing(false);
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {
                        if (data.getRows() != null && data.getRows().size() > 0) {
                            if (list == null) {
                                list = new ArrayList<>();
                            }
                            list.clear();
                            list.addAll(data.getRows());
                            adapter.setNewData(list);
                        }
                    } else {
                        list.clear();
                        adapter.setNewData(list);
                    }
                    adapter.setEnableLoadMore(false);
                }
            }
        });
    }



    @OnClick({R.id.tv_download_to_email})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_download_to_email://下载全部到邮箱
                RoutersUtils.open("ybmpage://aptitudexyyemail?flag=" + FLAG_ALL + "&contractId=" + IDS_ALL);
                break;
        }
    }

    private void getAptitudeXyyPdfUrl(String contractId) {//获取pdf合同的地址
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("contractId", contractId);
        HttpManager.getInstance().post(AppNetConfig.APTITUDE_XYY_PDF_DOWN_URL, params, new BaseResponse<AptitudePdfUrlBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudePdfUrlBean> baseBean, AptitudePdfUrlBean data) {
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {
                        DownloadFile(data.url);
                    }
                }
            }
        });
    }

    private void DownloadFile(String mFileUrl) {
        String mFileName = parseName(mFileUrl);

        AppUpdateManagerV2 updateManagerV2 = new AppUpdateManagerV2();
        updateManagerV2.setDownloadListener(new AppUpdateManagerV2.OnDownloadListener() {
            @Override
            public void onDownloadSuccess(File file) {
                dismissProgress();
                if (file != null && file.exists()) {
                    ShareUtil.sharePdfFileWechatFriend(getMySelf(), file);
                } else {
                    ToastUtils.showShort("分享的文件不存在");
                }
            }

            @Override
            public void onDownloading(int progress) {

            }

            @Override
            public void onDownloadFailed(Exception e) {
                dismissProgress();
                ToastUtils.showLong("pdf文件下载失败");
                BugUtil.sendBug(new Throwable("pdf文件下载失败"));
            }
        });
        updateManagerV2.downFile(mFileUrl, mFileName);
    }

    private String parseName(String url) {
        String fileName = null;
        try {
            if (url.contains("=")) {
                fileName = mTitle + url.substring(url.lastIndexOf("=") + 1);
            } else if (url.contains("/")) {
                fileName = mTitle + url.substring(url.lastIndexOf("/") + 1);
            }
        } catch (Exception e) {
            BugUtil.sendBug(e);
        } finally {
            if (TextUtils.isEmpty(fileName)) {
                fileName = mTitle + System.currentTimeMillis();
            }
        }
        fileName = fileName + ".pdf";
        return fileName;
    }

    @Override
    public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int i) {
        String title = "";
        String contractId = "";
        if (list != null && list.size() > 0) {
            title = list.get(i).getName();
            contractId = list.get(i).getId();
        }
        RoutersUtils.open("ybmpage://aptitudexyypdf?&title=" + title + "&contractId=" + contractId);
    }
}
